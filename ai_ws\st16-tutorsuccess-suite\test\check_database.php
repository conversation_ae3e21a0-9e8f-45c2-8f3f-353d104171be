<?php
/**
 * 检查数据库内容
 * 修订日期：2025-08-22
 */

// 设置错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "检查数据库内容...\n";

// 引入必要的文件
require_once 'web/ks1cck2/_lp.php';
require_once 'web/ks1cck2/education/config/database.php';

echo "数据库连接成功\n";

// 检查各个表的数据
$tables = [
    'edu_teacher' => '教师表',
    'edu_student' => '学生表', 
    'edu_course' => '课程表',
    'edu_user_role' => '用户角色表',
    'edu_leave_request' => '请假申请表',
    'edu_course_schedule' => '课程安排表',
    'edu_student_course' => '学生课程关联表'
];

foreach ($tables as $table => $description) {
    echo "\n=== $description ($table) ===\n";
    
    try {
        $sql = "SELECT COUNT(*) as count FROM $table";
        $count = get_var($sql);
        echo "记录数: $count\n";
        
        if ($count > 0) {
            $sql = "SELECT * FROM $table LIMIT 3";
            $data = get_data($sql);
            if ($data) {
                echo "示例数据:\n";
                foreach ($data as $row) {
                    echo "  " . json_encode($row, JSON_UNESCAPED_UNICODE) . "\n";
                }
            }
        }
    } catch (Exception $e) {
        echo "错误: " . $e->getMessage() . "\n";
    }
}

echo "\n检查完成！\n";
?>
